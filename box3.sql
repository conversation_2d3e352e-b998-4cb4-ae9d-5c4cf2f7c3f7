-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- <PERSON><PERSON><PERSON><PERSON>č: 127.0.0.1
-- Vytvořeno: Úte 12. srp 2025, 15:15
-- Verze serveru: 10.4.32-MariaDB
-- Verze PHP: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Datab<PERSON>ze: `box3`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON> tabulky `abort_transactions`
--

CREATE TABLE `abort_transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `type` varchar(255) NOT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `box_sections`
--

CREATE TABLE `box_sections` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `box_uuid` varchar(255) NOT NULL,
  `section_id` int(11) NOT NULL,
  `identification_name` varchar(255) NOT NULL,
  `tempered` int(11) NOT NULL DEFAULT 0,
  `visible` int(11) NOT NULL DEFAULT 1,
  `blocked` int(11) NOT NULL DEFAULT 0,
  `service` int(11) NOT NULL DEFAULT 0,
  `title` varchar(255) DEFAULT NULL,
  `lock_id` varchar(50) NOT NULL,
  `led_section` int(11) NOT NULL,
  `fixed_pin` tinyint(1) NOT NULL DEFAULT 0,
  `pin` varchar(255) DEFAULT NULL,
  `size_category` int(11) DEFAULT NULL,
  `size_width` int(11) DEFAULT NULL,
  `size_depth` int(11) DEFAULT NULL,
  `size_height` int(11) DEFAULT NULL,
  `mode` varchar(255) NOT NULL DEFAULT 'parcel',
  `type` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Vypisuji data pro tabulku `box_sections`
--

INSERT INTO `box_sections` (`id`, `uuid`, `box_uuid`, `section_id`, `identification_name`, `tempered`, `visible`, `blocked`, `service`, `title`, `lock_id`, `led_section`, `fixed_pin`, `pin`, `size_category`, `size_width`, `size_depth`, `size_height`, `mode`, `type`) VALUES
(1, '3603a9bd-f310-4d51-b39a-e4123d0b2b28', 'c580f199-9150-4662-a132-f12e5d7c625e', 1, '1', 1, 1, 0, 0, NULL, '1', 1, 0, NULL, 2, 20, 30, 40, 'storage', ''),
(2, '033f62cd-aed5-4cf6-bc4d-5ff94fa26277', 'c580f199-9150-4662-a132-f12e5d7c625e', 2, '2', 1, 1, 0, 0, NULL, '2', 2, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(3, '87bee335-ca9d-4600-ade7-7ecb9b1af320', 'c580f199-9150-4662-a132-f12e5d7c625e', 3, '3', 1, 1, 0, 0, NULL, '3', 3, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(4, 'b967d6e0-b1c1-4fff-8f10-e5424d6408d6', 'c580f199-9150-4662-a132-f12e5d7c625e', 4, '4', 1, 1, 0, 0, NULL, '4', 4, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(5, 'dd494dcc-552c-49ed-9c90-6190d88a65c3', 'c580f199-9150-4662-a132-f12e5d7c625e', 5, '5', 1, 1, 0, 0, NULL, '5', 5, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(6, '59c19c16-5997-4fa4-b128-a6e574fbef1b', 'c580f199-9150-4662-a132-f12e5d7c625e', 6, '6', 1, 1, 0, 0, NULL, '6', 6, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(7, 'b9a19a0c-83d5-420e-82a0-df8c47ea0b7a', 'c580f199-9150-4662-a132-f12e5d7c625e', 7, '7', 1, 1, 0, 0, NULL, '7', 7, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(8, 'ce9541d4-95da-42f5-b776-d1376fcc50fb', 'c580f199-9150-4662-a132-f12e5d7c625e', 8, '8', 1, 1, 0, 0, NULL, '8', 8, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(9, '5a5aca50-d9fe-4373-bae2-8f8a0dc686fc', 'c580f199-9150-4662-a132-f12e5d7c625e', 9, '9', 1, 1, 0, 0, NULL, '9', 9, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(10, 'ff3eb5ef-f0d1-4802-98da-f86e08300f7e', 'c580f199-9150-4662-a132-f12e5d7c625e', 10, '10', 1, 1, 0, 0, NULL, '10', 10, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(11, 'd23cc6e6-d0ea-479b-985f-2084071a34cf', 'c580f199-9150-4662-a132-f12e5d7c625e', 11, '11', 1, 1, 0, 0, NULL, '11', 11, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(12, 'd1c4409a-a5fd-4e28-a40c-5aeb1723c57d', 'c580f199-9150-4662-a132-f12e5d7c625e', 12, '12', 1, 1, 0, 0, NULL, '12', 12, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(13, '0bc9baf9-bba5-4ad8-ac72-83e49516aa4b', 'c580f199-9150-4662-a132-f12e5d7c625e', 13, '13', 1, 1, 0, 0, NULL, '13', 13, 0, NULL, 0, 20, 30, 40, 'sale', ''),
(14, 'd9ebfaf2-0eab-4af2-9fc2-6142bcd32367', 'c580f199-9150-4662-a132-f12e5d7c625e', 14, '14', 1, 1, 0, 0, NULL, '14', 14, 0, NULL, 0, 20, 30, 40, 'sale', '');

-- --------------------------------------------------------

--
-- Struktura tabulky `box_settings`
--

CREATE TABLE `box_settings` (
  `id` int(11) NOT NULL,
  `box_uuid` char(36) NOT NULL,
  `box_layout_id` int(11) NOT NULL,
  `box_layout` text NOT NULL,
  `changed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Vypisuji data pro tabulku `box_settings`
--

INSERT INTO `box_settings` (`id`, `box_uuid`, `box_layout_id`, `box_layout`, `changed_at`, `created_at`) VALUES
(1, 'c580f199-9150-4662-a132-f12e5d7c625e', 3, '{\n                \"columns\": [\n                    {\n                    \"width_percent\": 20,\n                    \"rows\": [\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"1\",\n                            \"width_percent\": 100,\n                            \"tempered\": 0,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"2\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.4,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"3\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        }\n                    ]\n                    },\n                    {\n                    \"width_percent\": 20,\n                    \"rows\": [\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"4\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"5\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.4,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"6\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        }\n                    ]\n                    },\n                    {\n                    \"width_percent\": 20,\n                    \"rows\": [\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"32\",\n                            \"width_percent\": 100,\n                            \"service\": 1,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"7\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.4,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"8\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        }\n                    ]\n                    },\n                    {\n                    \"width_percent\": 20,\n                    \"rows\": [\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"9\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"10\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.4,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"11\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        }\n                    ]\n                    },\n                    {\n                    \"width_percent\": 20,\n                    \"rows\": [\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"12\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.3,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"13\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        },\n                        {\n                        \"height_percent\": 33.4,\n                        \"cells\": [\n                            {\n                            \"section_id\": \"14\",\n                            \"width_percent\": 100,\n                            \"service\": 0,\n                            \"visible\": 1\n                            }\n                        ]\n                        }\n                    ]\n                    }\n                ]\n                }', '2025-07-26 14:28:39', '2025-07-26 14:28:39');

-- --------------------------------------------------------

--
-- Struktura tabulky `box_status`
--

CREATE TABLE `box_status` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `box_uuid` varchar(255) DEFAULT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `ambient_temperature` varchar(255) DEFAULT NULL,
  `box_temperature` varchar(255) DEFAULT NULL,
  `humidity` varchar(255) DEFAULT NULL,
  `compressor_run` varchar(255) DEFAULT NULL,
  `heating_run` varchar(255) DEFAULT NULL,
  `lighting_on` varchar(255) DEFAULT NULL,
  `power_supply` varchar(255) DEFAULT NULL,
  `box_open` varchar(255) DEFAULT NULL,
  `mac` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `camera_records`
--

CREATE TABLE `camera_records` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `box_uuid` char(36) NOT NULL,
  `start` varchar(255) NOT NULL,
  `end` varchar(255) NOT NULL,
  `download_link` varchar(255) NOT NULL,
  `thumbnail_link` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `close_totals_transactions`
--

CREATE TABLE `close_totals_transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `type` varchar(255) NOT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `commands`
--

CREATE TABLE `commands` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `params` text NOT NULL,
  `description` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `command_queues`
--

CREATE TABLE `command_queues` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `action` varchar(255) NOT NULL,
  `section` varchar(255) DEFAULT NULL,
  `execute_after` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `get_request_log`
--

CREATE TABLE `get_request_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `payload` varchar(255) DEFAULT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `notifications`
--

CREATE TABLE `notifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `mac` varchar(255) NOT NULL,
  `reservation_number` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `contact_type` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `content` text DEFAULT NULL,
  `contact` varchar(255) NOT NULL,
  `section_id` varchar(50) DEFAULT NULL,
  `section_title` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `entered_pin` varchar(50) DEFAULT NULL,
  `user` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `oauth_access_tokens`
--

CREATE TABLE `oauth_access_tokens` (
  `id` varchar(100) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `client_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `scopes` text DEFAULT NULL,
  `revoked` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `password_resets`
--

CREATE TABLE `password_resets` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `put_reservations`
--

CREATE TABLE `put_reservations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `reservation_uuid` varchar(255) DEFAULT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `reload`
--

CREATE TABLE `reload` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `command` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `request_log`
--

CREATE TABLE `request_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `payload` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `reservations`
--

CREATE TABLE `reservations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `customer_uuid` varchar(255) DEFAULT NULL,
  `box_uuid` varchar(255) DEFAULT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `reservation_number` varchar(255) DEFAULT NULL,
  `reservation_pin` varchar(255) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `age_control_required` tinyint(4) NOT NULL DEFAULT 0,
  `age_controlled` tinyint(4) NOT NULL DEFAULT 0,
  `price` double NOT NULL DEFAULT 0,
  `paid_status` varchar(255) DEFAULT NULL,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp(),
  `pin_attempt` int(11) NOT NULL DEFAULT 0,
  `ean` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `product_status` varchar(255) DEFAULT NULL,
  `cover_image` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `reservations_translations`
--

CREATE TABLE `reservations_translations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `reservation_id` bigint(20) UNSIGNED NOT NULL,
  `locale` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `reservation_content`
--

CREATE TABLE `reservation_content` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `reservation_uuid` varchar(255) DEFAULT NULL,
  `section_uuid` varchar(255) DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'inserted',
  `inserted_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `sale_reservations`
--

CREATE TABLE `sale_reservations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` char(36) NOT NULL DEFAULT uuid(),
  `box_uuid` varchar(255) DEFAULT NULL,
  `section_id` varchar(255) DEFAULT NULL,
  `ean` varchar(255) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `quantity` int(11) DEFAULT 1,
  `reserved` int(11) DEFAULT 0,
  `reservation_pin` varchar(255) DEFAULT NULL,
  `age_control_required` tinyint(4) DEFAULT 0,
  `age_controlled` tinyint(4) NOT NULL DEFAULT 0,
  `price` double NOT NULL DEFAULT 0,
  `max_days` int(11) DEFAULT NULL,
  `paid_status` varchar(255) DEFAULT NULL,
  `type` varchar(256) NOT NULL DEFAULT 'ean',
  `cover_image` varchar(255) DEFAULT NULL,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Vypisuji data pro tabulku `sale_reservations`
--

INSERT INTO `sale_reservations` (`id`, `uuid`, `box_uuid`, `section_id`, `ean`, `status`, `name`, `description`, `quantity`, `reserved`, `reservation_pin`, `age_control_required`, `age_controlled`, `price`, `max_days`, `paid_status`, `type`, `cover_image`, `last_update`, `created_at`) VALUES
(0, '4cc74b43-8404-48bc-bd37-342312cebc31', NULL, '2', NULL, 1, NULL, NULL, 1, 0, NULL, 0, 0, 1, NULL, NULL, 'custom', NULL, '2025-08-12 08:41:41', '2025-08-12 08:41:41');

-- --------------------------------------------------------

--
-- Struktura tabulky `sale_transactions`
--

CREATE TABLE `sale_transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `type` varchar(255) NOT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `result` varchar(255) DEFAULT NULL,
  `request` text DEFAULT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `server_requests_queue`
--

CREATE TABLE `server_requests_queue` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `status` smallint(5) UNSIGNED NOT NULL,
  `order_id` varchar(255) NOT NULL,
  `endpoint` varchar(255) NOT NULL,
  `action` varchar(255) NOT NULL,
  `error` varchar(255) DEFAULT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`payload`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `set_request_log`
--

CREATE TABLE `set_request_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `payload` varchar(255) DEFAULT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Struktura tabulky `storage_categories`
--

CREATE TABLE `storage_categories` (
  `id` int(11) NOT NULL,
  `size_category` int(11) NOT NULL,
  `price` int(11) NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Vypisuji data pro tabulku `storage_categories`
--

INSERT INTO `storage_categories` (`id`, `size_category`, `price`, `updated_at`, `created_at`) VALUES
(1, 2, 59, '2025-08-08 07:04:40', '2025-08-08 07:04:40');

-- --------------------------------------------------------

--
-- Struktura tabulky `storage_reservations`
--

CREATE TABLE `storage_reservations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` char(36) NOT NULL DEFAULT uuid(),
  `box_uuid` varchar(255) DEFAULT NULL,
  `section_id` varchar(255) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `reservation_pin` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `price` double NOT NULL DEFAULT 0,
  `max_days` int(11) DEFAULT NULL,
  `paid_status` varchar(255) DEFAULT NULL,
  `category` int(11) DEFAULT NULL,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Vypisuji data pro tabulku `storage_reservations`
--

INSERT INTO `storage_reservations` (`id`, `uuid`, `box_uuid`, `section_id`, `status`, `reservation_pin`, `email`, `price`, `max_days`, `paid_status`, `category`, `last_update`, `created_at`) VALUES
(1, '84e7997e-a01b-41d1-b354-e1bf77ccc94e', 'c580f199-9150-4662-a132-f12e5d7c625e', '1', 0, '618847', '<EMAIL>', 59, 25, 'paid', 2, '2025-08-12 08:40:57', '2025-08-12 08:40:57');

-- --------------------------------------------------------

--
-- Struktura tabulky `storage_transactions`
--

CREATE TABLE `storage_transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `type` varchar(255) NOT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `result` varchar(255) DEFAULT NULL,
  `request` text DEFAULT NULL,
  `response` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Vypisuji data pro tabulku `storage_transactions`
--

INSERT INTO `storage_transactions` (`id`, `uuid`, `date_time`, `type`, `msg`, `result`, `request`, `response`) VALUES
(1, 'f0982a95-f336-4ba7-8ce2-d8408d392e5f', '2025-08-12 08:40:49', 'storage', NULL, 'initiating', '{\"type\": \"sale\", \"amount\": 59.0, \"variable_symbol\": \"f0982a95-f336-4ba7-8ce2-d8408d392e5f\"}', NULL),
(2, 'f0982a95-f336-4ba7-8ce2-d8408d392e5f', '2025-08-12 08:40:49', 'storage', NULL, 'success', NULL, '{\"status_code\": 200, \"text\": \"{\\\"status\\\": \\\"success\\\", \\\"msg\\\": \\\"accepted\\\"}\"}');

-- --------------------------------------------------------

--
-- Struktura tabulky `timeline_log`
--

CREATE TABLE `timeline_log` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `serial_number` varchar(255) DEFAULT NULL,
  `entered_pin` varchar(50) DEFAULT NULL,
  `event_type` varchar(50) DEFAULT NULL,
  `event_result` varchar(255) DEFAULT NULL,
  `operator_id` varchar(255) DEFAULT NULL,
  `section_id` int(11) DEFAULT NULL,
  `tempered_unlock` int(11) DEFAULT NULL,
  `box_status` varchar(255) DEFAULT NULL,
  `message` varchar(255) DEFAULT NULL,
  `mode` varchar(255) DEFAULT NULL,
  `session_id` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Vypisuji data pro tabulku `timeline_log`
--

INSERT INTO `timeline_log` (`id`, `serial_number`, `entered_pin`, `event_type`, `event_result`, `operator_id`, `section_id`, `tempered_unlock`, `box_status`, `message`, `mode`, `session_id`, `created_at`) VALUES
(1, 'xxxx', '3AF9E871C', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (hygiene)', 'hygiene', 'operator_67303468-76e3-41dd-9e36-75df74c29665', '2025-08-08 07:37:57'),
(2, 'xxxx', '3AF9E871C', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (hygiene)', 'hygiene', 'operator_6a8e7ad0-a010-4b0e-a0d9-48296cbc73da', '2025-08-08 07:45:57'),
(3, 'xxxx', '3AF9E871C', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (hygiene)', 'hygiene', 'operator_8989f589-58a6-4cc9-b9de-523d59b3a7cf', '2025-08-08 07:46:12'),
(4, 'xxxx', '3AF9E871C', 'service_pin_entered', 'failed_to_check', NULL, NULL, NULL, NULL, 'Unexpected error during server check: \'str\' object has no attribute \'pop\'', 'unknown', NULL, '2025-08-08 07:46:12'),
(5, 'xxxx', '3AF9E871C', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (hygiene)', 'hygiene', 'operator_387c7df4-1ff3-4b3c-88ff-ccf6528edaf4', '2025-08-08 07:46:17'),
(6, 'xxxx', '3AF9E871C', 'service_pin_entered', 'failed_to_check', NULL, NULL, NULL, NULL, 'Unexpected error during server check: \'str\' object has no attribute \'pop\'', 'unknown', NULL, '2025-08-08 07:46:17'),
(7, 'xxxx', '3AF9E871C', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (hygiene)', 'hygiene', 'operator_fce73457-b3c5-48e2-b9d9-eecb39df0151', '2025-08-08 07:46:31'),
(8, 'xxxx', '3AF9E871C', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (hygiene)', 'hygiene', 'operator_3bd1063c-f5c6-4744-b265-a6dc77a1907d', '2025-08-08 07:48:42'),
(9, 'xxxx', '3AF9E871C', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (hygiene)', 'hygiene', 'operator_080266b1-6fd8-4c4f-b598-caacd613b479', '2025-08-08 08:05:50'),
(10, 'xxxx', '3AF9E871C', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (hygiene)', 'hygiene', 'operator_fe95a95c-ed9b-47a6-ac09-d36cef950cda', '2025-08-08 08:27:27'),
(11, 'xxxx', '3179F4EFDA', 'service_pin_entered', 'env_check_initiated', NULL, NULL, NULL, NULL, '10-character PIN entered, checking against .env configuration', 'operator', NULL, '2025-08-08 08:27:45'),
(12, 'xxxx', '3179F4EFDA', 'service_pin_entered', 'allowed', 'default', NULL, NULL, NULL, '10-character PIN allowed - GS Service', 'service', 'operator_4e2685ef-a9ff-4b40-8f84-6adc9e7834d1', '2025-08-08 08:27:45'),
(13, 'xxxx', '3179F4EFD', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (courier)', 'courier', 'operator_037fddf2-fcb1-4688-81f8-0e497930bcc8', '2025-08-08 08:38:07'),
(14, 'xxxx', '3AF9E871C', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (courier)', 'courier', 'operator_84d930a3-e909-484c-903c-82110e7b58fd', '2025-08-08 08:38:42'),
(15, 'xxxx', '3AF9E871C', 'service_pin_entered', 'allowed', '1', NULL, NULL, NULL, '9-character PIN allowed by server - Tung Sahur (hygiene)', 'hygiene', 'operator_bd08dd8d-bf0b-463e-a9c3-46b13bb7523d', '2025-08-08 08:43:12'),
(16, 'xxxx', '3AF9E871C', 'service_pin_entered', 'failed_to_check', NULL, NULL, NULL, NULL, 'No response data from server', 'unknown', NULL, '2025-08-08 09:28:58'),
(17, '1234', NULL, 'mqtt_command', 'success', NULL, NULL, NULL, NULL, 'Device unlocked successfully', 'mqtt', NULL, '2025-08-11 13:14:26'),
(18, '1234', NULL, 'unlock', 'success', NULL, NULL, 0, NULL, 'Device unlocked via MQTT (unlock)', 'mqtt', NULL, '2025-08-11 13:14:26'),
(19, '1234', NULL, 'mqtt_command', 'success', NULL, NULL, NULL, NULL, 'Device unlocked successfully', 'mqtt', NULL, '2025-08-11 13:34:55'),
(20, '1234', NULL, 'unlock', 'success', NULL, NULL, 0, NULL, 'Device unlocked via MQTT (unlock)', 'mqtt', NULL, '2025-08-11 13:34:55'),
(21, '1234', NULL, 'mqtt_command', 'success', NULL, NULL, NULL, NULL, 'Device unlocked successfully', 'mqtt', NULL, '2025-08-11 14:01:24'),
(22, '1234', NULL, 'unlock', 'success', NULL, NULL, 0, NULL, 'Device unlocked via MQTT (unlock)', 'mqtt', NULL, '2025-08-11 14:01:24'),
(23, '1234', NULL, 'mqtt_command', 'success', NULL, NULL, NULL, NULL, 'Device unlocked successfully', 'mqtt', NULL, '2025-08-11 14:01:28'),
(24, '1234', NULL, 'unlock', 'success', NULL, NULL, 0, NULL, 'Device unlocked via MQTT (unlock)', 'mqtt', NULL, '2025-08-11 14:01:28'),
(25, 'xxxx', NULL, 'open_lock', 'failed', NULL, 1, 1, NULL, 'Result code: -20', 'mqtt', NULL, '2025-08-12 07:40:47'),
(26, 'xxxx', NULL, 'open_lock', 'failed', NULL, 1, 1, NULL, 'Result code: -20', 'mqtt', NULL, '2025-08-12 07:59:10'),
(27, 'xxxx', NULL, 'open_lock', 'failed', NULL, 1, 1, NULL, 'Result code: -20', 'mqtt', NULL, '2025-08-12 08:02:43'),
(28, 'xxxx', NULL, 'storage_process', 'started', NULL, 1, NULL, NULL, NULL, 'storage', NULL, '2025-08-12 08:40:22'),
(29, 'xxxx', NULL, 'new_transaction', 'payment_process_started', NULL, NULL, NULL, NULL, NULL, 'storage', 'f0982a95-f336-4ba7-8ce2-d8408d392e5f', '2025-08-12 08:40:49'),
(30, 'xxxx', NULL, 'payment_callback', 'payment_callback_success', NULL, NULL, NULL, NULL, NULL, 'storage', 'f0982a95-f336-4ba7-8ce2-d8408d392e5f', '2025-08-12 08:40:49'),
(31, 'xxxx', NULL, 'create_reservaion', 'success', NULL, 1, NULL, NULL, 'Reservation created with PIN: 618847', 'storage', NULL, '2025-08-12 08:40:57'),
(32, 'xxxx', NULL, 'open_lock', 'failed', NULL, 1, 1, NULL, 'Result code: -20', 'storage', NULL, '2025-08-12 08:41:06'),
(33, 'xxxx', NULL, 'product_insert', 'success', NULL, 2, NULL, NULL, 'Custom product inserted successfully in section 2 with price 3', 'product', NULL, '2025-08-12 08:41:41'),
(34, 'xxxx', NULL, 'product_insert', 'section_occupied', NULL, 2, NULL, NULL, 'Section 2 already has an active product', 'product', NULL, '2025-08-12 08:42:09');

-- --------------------------------------------------------

--
-- Struktura tabulky `transactions`
--

CREATE TABLE `transactions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `date_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `type` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `payload` text DEFAULT NULL,
  `auth_code` varchar(255) DEFAULT NULL,
  `content` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexy pro exportované tabulky
--

--
-- Indexy pro tabulku `abort_transactions`
--
ALTER TABLE `abort_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `abort_transactions_uuid_unique` (`uuid`),
  ADD KEY `abort_transactions_uuid_index` (`uuid`);

--
-- Indexy pro tabulku `box_sections`
--
ALTER TABLE `box_sections`
  ADD PRIMARY KEY (`id`);

--
-- Indexy pro tabulku `box_settings`
--
ALTER TABLE `box_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `id` (`id`);

--
-- Indexy pro tabulku `box_status`
--
ALTER TABLE `box_status`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `box_status_uuid_unique` (`uuid`),
  ADD KEY `box_status_uuid_index` (`uuid`),
  ADD KEY `box_status_box_uuid_index` (`box_uuid`),
  ADD KEY `box_status_mac_index` (`mac`);

--
-- Indexy pro tabulku `camera_records`
--
ALTER TABLE `camera_records`
  ADD PRIMARY KEY (`id`),
  ADD KEY `camera_records_box_uuid_foreign` (`box_uuid`);

--
-- Indexy pro tabulku `close_totals_transactions`
--
ALTER TABLE `close_totals_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `close_totals_transactions_uuid_unique` (`uuid`),
  ADD KEY `close_totals_transactions_uuid_index` (`uuid`);

--
-- Indexy pro tabulku `commands`
--
ALTER TABLE `commands`
  ADD PRIMARY KEY (`id`);

--
-- Indexy pro tabulku `command_queues`
--
ALTER TABLE `command_queues`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `command_queues_uuid_unique` (`uuid`),
  ADD KEY `command_queues_uuid_index` (`uuid`),
  ADD KEY `command_queues_action_index` (`action`),
  ADD KEY `command_queues_section_index` (`section`),
  ADD KEY `command_queues_execute_after_index` (`execute_after`);

--
-- Indexy pro tabulku `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexy pro tabulku `get_request_log`
--
ALTER TABLE `get_request_log`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `get_request_log_uuid_unique` (`uuid`),
  ADD KEY `get_request_log_uuid_index` (`uuid`);

--
-- Indexy pro tabulku `sale_reservations`
--
ALTER TABLE `sale_reservations`
  ADD UNIQUE KEY `id` (`id`);

--
-- Indexy pro tabulku `storage_categories`
--
ALTER TABLE `storage_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexy pro tabulku `storage_reservations`
--
ALTER TABLE `storage_reservations`
  ADD PRIMARY KEY (`id`);

--
-- Indexy pro tabulku `storage_transactions`
--
ALTER TABLE `storage_transactions`
  ADD PRIMARY KEY (`id`);

--
-- Indexy pro tabulku `timeline_log`
--
ALTER TABLE `timeline_log`
  ADD PRIMARY KEY (`id`);

--
-- Indexy pro tabulku `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT pro tabulky
--

--
-- AUTO_INCREMENT pro tabulku `box_sections`
--
ALTER TABLE `box_sections`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT pro tabulku `box_settings`
--
ALTER TABLE `box_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT pro tabulku `storage_categories`
--
ALTER TABLE `storage_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pro tabulku `storage_reservations`
--
ALTER TABLE `storage_reservations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT pro tabulku `storage_transactions`
--
ALTER TABLE `storage_transactions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pro tabulku `timeline_log`
--
ALTER TABLE `timeline_log`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=35;

--
-- AUTO_INCREMENT pro tabulku `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
